<?php
if (!defined('ABSPATH')) {
    die('-1');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST[$this->FCRE->plugin_name . '-property-field-order'])) {
        $field_order = sanitize_text_field($_POST[$this->FCRE->plugin_name . '-property-field-order']);
        update_option($this->FCRE->plugin_name . '-property-field-order', $field_order);
    }
    
    if (isset($_POST[$this->FCRE->plugin_name . '-property-field-visibility'])) {
        $field_visibility = array_map('sanitize_text_field', $_POST[$this->FCRE->plugin_name . '-property-field-visibility']);
        update_option($this->FCRE->plugin_name . '-property-field-visibility', $field_visibility);
    }
}

// Define all available property detail fields with their labels and meta keys
$available_fields = [
    'property_type' => [
        'label' => 'Property Type',
        'meta_key' => 'property_types',
        'type' => 'dynamic'
    ],
    'transaction_type' => [
        'label' => 'Transaction Type',
        'meta_key' => 'transaction_types',
        'type' => 'dynamic'
    ],
    'address' => [
        'label' => 'Address',
        'meta_key' => 'address',
        'type' => 'text'
    ],
    'site' => [
        'label' => 'Site',
        'meta_key' => 'site',
        'type' => 'text'
    ],
    'building' => [
        'label' => 'Building',
        'meta_key' => 'building',
        'type' => 'text'
    ],
    'building_size' => [
        'label' => 'Building Size',
        'meta_key' => 'building_size',
        'type' => 'number'
    ],
    'building_class' => [
        'label' => 'Building Class',
        'meta_key' => 'building_class',
        'type' => 'text'
    ],
    'lease_rate' => [
        'label' => 'Lease Rate',
        'meta_key' => 'lease_rate',
        'type' => 'text'
    ],
    'year_built' => [
        'label' => 'Year Built',
        'meta_key' => 'year_built',
        'type' => 'number'
    ],
    'lease_rate_suffix' => [
        'label' => 'Lease Rate Suffix',
        'meta_key' => 'lease_rate_suffix',
        'type' => 'text'
    ],
    'price' => [
        'label' => 'Price',
        'meta_key' => 'price',
        'type' => 'number'
    ],
    'occupancy' => [
        'label' => 'Occupancy',
        'meta_key' => 'occupancy',
        'type' => 'text'
    ],
    'cap_rate' => [
        'label' => 'Cap Rate',
        'meta_key' => 'cap_rate',
        'type' => 'number'
    ],
    'noi' => [
        'label' => 'NOI',
        'meta_key' => 'noi',
        'type' => 'number'
    ],
    'parking_ratio' => [
        'label' => 'Parking Ratio',
        'meta_key' => 'parking_ratio',
        'type' => 'text'
    ],
    'total_parking_spaces' => [
        'label' => 'Total Parking Spaces',
        'meta_key' => 'total_parking_spaces',
        'type' => 'text'
    ],
    'loading_docks' => [
        'label' => 'Loading Docks',
        'meta_key' => 'loading_docks',
        'type' => 'text'
    ],
    'zoning' => [
        'label' => 'Zoning',
        'meta_key' => 'zoning',
        'type' => 'text'
    ],
    'sprinkler_system' => [
        'label' => 'Sprinkler System',
        'meta_key' => 'sprinkler_system',
        'type' => 'text'
    ],
    'hvac_type' => [
        'label' => 'HVAC Type',
        'meta_key' => 'hvac_type',
        'type' => 'text'
    ],
    'power' => [
        'label' => 'Power',
        'meta_key' => 'power',
        'type' => 'text'
    ],
    'connectivity' => [
        'label' => 'Fiber / Connectivity',
        'meta_key' => 'connectivity',
        'type' => 'text'
    ],
    'elevators' => [
        'label' => 'Elevators',
        'meta_key' => 'elevators',
        'type' => 'text'
    ]
];

// Get saved field order and visibility
$saved_field_order = get_option($this->FCRE->plugin_name . '-property-field-order', '');
$saved_field_visibility = get_option($this->FCRE->plugin_name . '-property-field-visibility', array_keys($available_fields));

// If no saved order, use default order
if (empty($saved_field_order)) {
    $field_order = array_keys($available_fields);
} else {
    $field_order = explode(',', $saved_field_order);
    // Ensure all fields are included (in case new fields were added)
    $field_order = array_merge($field_order, array_diff(array_keys($available_fields), $field_order));
}

?>

<h3 class="card-title">Field Order & Visibility</h3>
<p>Drag and drop to reorder fields. Uncheck fields to hide them from the frontend display.</p>
    
<div id="fcre-field-sorting-container">
    <ul id="fcre-sortable-fields" class="fcre-sortable-list">
        <?php foreach ($field_order as $field_key): ?>
            <?php if (isset($available_fields[$field_key])): ?>
                <li class="fcre-sortable-item" data-field="<?php echo esc_attr($field_key); ?>">
                    <div class="fcre-field-item">
                        <span class="fcre-drag-handle">⋮⋮</span>
                        <label class="fcre-field-label">
                            <input type="checkbox" 
                                   name="<?php echo $this->FCRE->plugin_name; ?>-property-field-visibility[]" 
                                   value="<?php echo esc_attr($field_key); ?>"
                                   <?php checked(in_array($field_key, $saved_field_visibility)); ?>>
                            <span class="fcre-field-name"><?php echo esc_html($available_fields[$field_key]['label']); ?></span>
                        </label>
                        <span class="fcre-field-meta"><?php echo esc_html($available_fields[$field_key]['meta_key']); ?></span>
                    </div>
                </li>
            <?php endif; ?>
        <?php endforeach; ?>
    </ul>
</div>

<style>
.fcre-sortable-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

.fcre-sortable-item {
    border-bottom: 1px solid #eee;
    cursor: move;
    transition: background-color 0.2s;
    margin: 0;
}

.fcre-sortable-item:last-child {
    border-bottom: none;
}

.fcre-sortable-item:hover {
    background-color: #f9f9f9;
}

.fcre-sortable-item.ui-sortable-helper {
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    border: 1px solid #ddd;
}

.fcre-field-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    gap: 10px;
}

.fcre-drag-handle {
    color: #666;
    font-size: 14px;
    cursor: move;
    user-select: none;
}

.fcre-field-label {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    margin: 0;
    cursor: pointer;
}

.fcre-field-name {
    font-weight: 500;
    color: #333;
}

.fcre-field-meta {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
}

.fcre-field-sorting-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.fcre-field-sorting-actions .button {
    margin-right: 10px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize sortable
    $('#fcre-sortable-fields').sortable({
        handle: '.fcre-drag-handle',
        placeholder: 'fcre-sortable-placeholder',
        update: function(event, ui) {
            updateFieldOrder();
        }
    });
    
    function updateFieldOrder() {
        var fieldOrder = [];
        $('#fcre-sortable-fields .fcre-sortable-item').each(function() {
            fieldOrder.push($(this).data('field'));
        });
        $('#fcre-field-order-input').val(fieldOrder.join(','));
    }
    
    // Reset to default order
    $('#fcre-reset-field-order').on('click', function() {
        if (confirm('Are you sure you want to reset the field order to default? This will also make all fields visible.')) {
            // Reset checkboxes to checked
            $('#fcre-sortable-fields input[type="checkbox"]').prop('checked', true);
            
            // Reset order (this would require a page reload to see the default order)
            $('#fcre-field-order-input').val('');
            
            // Submit form to save changes
            $(this).closest('form').submit();
        }
    });
});
</script>
