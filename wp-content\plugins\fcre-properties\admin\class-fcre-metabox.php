<?php

/**
 * Add Metaboxes to the plugin.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/admin
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_Metabox {

	private $FCRE;
	private $plugin_name;
	private $version;


	public function __construct(  ) {

		$this->FCRE = Fcre_Global::getInstance();
		$this->plugin_name = $this->FCRE->plugin_name;
		$this->version = $this->FCRE->version;

	}
 
 
		
	public function snapcre_add_custom_box()
    {
        //$screens = array('myskills');
        add_meta_box(
            $this->FCRE->plugin_name . '-metabox',
            __(
                '<strong>Property Details</strong>',
                $this->FCRE->plugin_name . '_textdomain'
            ),
            array($this, 'snapcre_inner_custom_box'),
            $this->FCRE->properties_custom_post_slug
        );
    }

	public function snapcre_inner_custom_box($post)
	{
		wp_nonce_field('fcre_save_metabox', 'fcre_metabox_nonce');


		
		$tabs = [
			['slug' => 'overview', 'label' => 'Overview', 'file' => 'overview.php'],
			['slug' => 'documents', 'label' => 'Documents', 'file' => 'documents.php'],
			['slug' => 'photos', 'label' => 'Photos', 'file' => 'photos.php'],
			['slug' => 'siteplans', 'label' => 'Site Plans', 'file' => 'siteplans.php'],
			['slug' => 'floorplans', 'label' => 'Floor Plans', 'file' => 'floorplans.php'],
			['slug' => 'agents', 'label' => 'Agents', 'file' => 'agents.php'],
			['slug' => 'demographics', 'label' => 'Demographics', 'file' => 'demographics.php'],
			['slug' => 'confidentiality_agreement', 'label' => 'Confidentiality Agreement', 'file' => 'confidentiality_agreement.php']
		];
	
		echo '<div class="fcre-tabs">';
		echo '<ul class="fcre-tab-titles">';
		foreach ($tabs as $tab) {
			echo '<li data-tab="' . esc_attr($tab['slug']) . '">' . esc_html($tab['label']) . '</li>';
		}
		echo '</ul>';
	
		foreach ($tabs as $tab) {
			echo '<div class="fcre-tab-content" id="tab-' . esc_attr($tab['slug']) . '" style="display: none;">';
			include __DIR__ . '/partials/metabox/property/' . $tab['file'];
			echo '</div>';
		}
		echo '</div>';
	
		// Include simple JS for tabs
		?>
		<script>
			jQuery(document).ready(function($){
				$('.fcre-tab-titles li').click(function(){
					var tab_id = $(this).data('tab');
					$('.fcre-tab-content').hide();
					$('#tab-' + tab_id).show();
					$('.fcre-tab-titles li').removeClass('active');
					$(this).addClass('active');
				});
				$('.fcre-tab-titles li:first').click();
			});
		</script>
		<?php
	 }
  
	public function fcre_save_properties_metabox($post_id)
	{
		if (!isset($_POST['fcre_metabox_nonce']) || !wp_verify_nonce($_POST['fcre_metabox_nonce'], 'fcre_save_metabox')) return;
		if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
		if (!current_user_can('edit_post', $post_id)) return;


		$fields_to_save = [
			'transaction_types',
			'property_types',
			'address',
			'site',
			'building',
			'building_size',
			'building_class',
			'year_built',
			'status',
			'latitude',
			'longitude',
			'property_flyer',
			'property_om',
			'documents_gallery',
			'photo_gallery',
			'site_plans',
			'floor_plans',
			'virtual_tour',
			'property_video',
			'demographics',
			'related_agents',
			'ca_check',
			'is_global_ca_check',
			'confidential_agreement',
			'flyer_ca_required',
			'om_ca_required',
			'lease_rate',
			'lease_rate_suffix',
			'price',
			'occupancy',
			'cap_rate',
			'noi',
			'parking_ratio',
			'total_parking_spaces',
			'loading_docks',
			'zoning',
			'sprinkler_system',
			'hvac_type',
			'power',
			'connectivity',
			'elevators'
		];
	
		foreach ($fields_to_save as $field) {
			if (isset($_POST[$field])) {
				update_post_meta($post_id, $field, $_POST[$field]);
			}else{
				delete_post_meta($post_id, $field);
			}
		}
		
	}

	public function fcre_agent_add_custom_box()
    {
        add_meta_box(
            $this->FCRE->plugin_name . '-metabox',
            __(
                '<strong>Agent Details</strong>',
                $this->FCRE->plugin_name . '_textdomain'
            ),
            array($this, 'fcre_agent_inner_custom_box'),
            $this->FCRE->agent_custom_post_slug
        );
    }

	public function fcre_agent_inner_custom_box($post)
	{
		wp_nonce_field('fcre_save_metabox', 'fcre_metabox_nonce');
		include __DIR__ . '/partials/metabox/agent/agent.php';
	}

	public function fcre_save_agent_metabox($post_id)
	{
		if (!isset($_POST['fcre_metabox_nonce']) || !wp_verify_nonce($_POST['fcre_metabox_nonce'], 'fcre_save_metabox')) return;
		if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
		if (!current_user_can('edit_post', $post_id)) return;

		$fields_to_save = [
			'designation',
			'email',
			'phone',
		];

		foreach ($fields_to_save as $field) {
			if (isset($_POST[$field])) {
				update_post_meta($post_id, $field, $_POST[$field]);
			}else{
				delete_post_meta($post_id, $field);
			}
		}
	}

}
