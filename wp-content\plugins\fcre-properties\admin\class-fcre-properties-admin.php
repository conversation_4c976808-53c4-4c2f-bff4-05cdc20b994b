<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/admin
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_Properties_Admin
{

	private $FCRE;
	private $plugin_name;
	private $version;
	private $active_tab = 'fcre-main';

	private $tabs = [
		'fcre-main' => 'Property & Transactions Types',
		'fcre-email' => 'Admin Email',
		'fcre-colors' => 'Colors',
		'fcre-single-property' => 'Single Property Page',
		// 'fcre-filter' => 'Filters, Views and Sorting',
		// 'fcre-thumbnail-fields' => 'Thumbnail Fields',
		// 'fcre-confidentiality-agreement' => 'Confidentiality Agreement',
		// 'fcre-ca' => 'Property Detail Page',
		// 'fcre-formbuilder' => 'Form Builder',
		// 'fcre-sharing' => 'Sharing',
		// 'fcre-pagination' => 'Pagination',
		// 'fcre-registration' => 'User Registration',
		// 'fcre-general-settings' => 'General Settings',
		// 'fcre-widget' => 'Search Widget',
		// 'fcre-shortcode' => 'Shortcodes',
		// 'fcre-map' => 'Map & reCAPTCHA',
		// 'fcre-api' => 'API',
		// 'fcre-branding' => 'Branding',
		// 'fcre-custom-css' => 'Custom CSS',
	];


	public function __construct()
	{

		$this->FCRE = Fcre_Global::getInstance();
		$this->plugin_name = $this->FCRE->plugin_name;
		$this->version = $this->FCRE->version;
	}

	/**
	 * Register the stylesheets for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Fcre_Properties_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Fcre_Properties_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */
		wp_enqueue_style('select2-css', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
		wp_enqueue_style($this->plugin_name, plugin_dir_url(__FILE__) . 'css/fcre-properties-admin.css', array(), $this->version, 'all');

		wp_enqueue_media();
		wp_enqueue_script('media-uploader');
	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Fcre_Properties_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Fcre_Properties_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_editor();
		wp_enqueue_style('wp-color-picker');
		wp_enqueue_script('wp-color-picker');

		wp_enqueue_script('select2-js', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), null, true);

		wp_enqueue_script($this->plugin_name . '-admin', plugin_dir_url(__FILE__) . 'js/fcre-properties-admin.js', array('jquery', 'select2-js', 'wp-color-picker'), $this->version, true);
		wp_enqueue_script($this->plugin_name . '-file-gallery', plugin_dir_url(__FILE__) . 'js/fcre-file-gallery.js', array('jquery', 'jquery-ui-sortable', 'media-upload', 'media-views'), $this->version, true);
		wp_enqueue_script($this->plugin_name . '-photo-gallery', plugin_dir_url(__FILE__) . 'js/fcre-photo-gallery.js', array('jquery', 'jquery-ui-sortable', 'media-upload', 'media-views'), $this->version, true);
		wp_enqueue_script($this->plugin_name . '-repeater', plugin_dir_url(__FILE__) . 'js/fcre-repeater.js', array('jquery', 'jquery-ui-sortable', 'media-upload', 'media-views'), $this->version, true);
	}

	/**
	 * Plugin admin menu.
	 *
	 * @since     1.0.0
	 *
	 */
	public function fcre_plugin_menu()
	{
		add_menu_page(
			'SnapCRE',
			'SnapCRE',
			'edit_posts',
			$this->FCRE->plugin_name,
			'my_plugin_dashboard',
			'dashicons-building',
			// plugin_dir_url(__FILE__) . 'img/icon.svg',
			'12'
		);

		// add_submenu_page($this->FCRE->plugin_name, 'Properties Category', 'Properties Category', 'manage_options', 'edit-tags.php?taxonomy=cat_properties');
		// $this->fcre_activity_log_menu();
		// $this->fcre_user_menu();
		add_submenu_page($this->FCRE->plugin_name, 'Settings', 'Settings', 'manage_options', $this->FCRE->plugin_name . '-settings', array($this, 'settings_page'));
	}



	public function settings_page()
	{

		$this->active_tab = $_GET['tab'] ?? 'fcre-main';
		if (!array_key_exists($this->active_tab, $this->tabs)) {
			$this->active_tab = 'fcre-main';
		}

?>

		<div class="fcre-tab-header">
			<h2><?php echo esc_html__('Settings', 'fcre'); ?></h2>
			<p><?php echo esc_html__('Configure the settings for the plugin.', 'fcre'); ?></p>
		</div>

		<div class="fcre-tab-section">
			<div class="fcre-tab-sidebar">
				<div class="nav-tab-wrapper fcre-tabs">
					<?php foreach ($this->tabs as $key => $label) : ?>
						<a href="?page=fcre-properties-settings&tab=<?php echo esc_attr($key); ?>"
							class="nav-tab <?php echo ($this->active_tab == $key) ? 'nav-tab-active' : ''; ?>">
							<?php echo esc_html__($label, 'fcre'); ?>
						</a>
					<?php endforeach; ?>
				</div>
			</div>
			<div class="fcre-tab-content">

				<form action="" method="post">
					<?php
					$form_file = plugin_dir_path(dirname(__FILE__)) . 'admin/partials/settings/' . str_replace('fcre-', '', $this->active_tab) . '-tab.php';

					if (file_exists($form_file)) {
						require_once $form_file;
					}
					?>
					<div class="fcre-tab-footer">
						<button type="submit" class="fcre-btn-save button button-primary"><?php echo esc_html__('Save Settings', 'fcre'); ?></button>
						<?php wp_nonce_field('fcre_save_settings', 'fcre_save_settings_nonce'); ?>
						<input type="hidden" name="fcre_settings_tab" value="<?php echo esc_attr($this->active_tab); ?>">
						<input type="hidden" name="fcre_settings_action" value="save">
						<input type="hidden" name="fcre_settings_nonce" value="<?php echo esc_attr(wp_create_nonce('fcre_settings_nonce')); ?>">
					</div>
				</form>
			</div>
		</div>
<?php


	}

	public function fcre_filter_properties()
	{
		$args = [
			'post_type'      => $this->FCRE->properties_custom_post_slug,
			'posts_per_page' => -1,
			'post_status'    => 'publish',
			'meta_query'     => [],
			'tax_query'      => [],
		];

		if (isset($_POST['markers_in_viewport']) && $_POST['markers_in_viewport']) {
			$args['post__in'] = $_POST['markers_in_viewport'];
		}


		if (!empty($_POST['property_types'])) {
			$meta_queries = [];

			foreach ($_POST['property_types'] as $type) {
				$meta_queries[] = [
					'key'     => 'property_types',
					'value'   => '"' . $type . '"',
					'compare' => 'LIKE',
				];
			}

			$args['meta_query'][] = array_merge(['relation' => 'OR'], $meta_queries);
		}


		if (!empty($_POST['transaction_types'])) {
			$meta_queries = [];

			foreach ($_POST['transaction_types'] as $type) {
				$meta_queries[] = [
					'key'     => 'transaction_types',
					'value'   => '"' . $type . '"',
					'compare' => 'LIKE',
				];
			}

			$args['meta_query'][] = array_merge(['relation' => 'OR'], $meta_queries);
		}


		if (!empty($_POST['min-sf']) || !empty($_POST['max-sf'])) {
			$building_size_range = [];
			if (!empty($_POST['min-sf'])) {
				$building_size_range['min'] = (int) $_POST['min-sf'];
			}
			if (!empty($_POST['max-sf'])) {
				$building_size_range['max'] = (int) $_POST['max-sf'];
			}
			if (!empty($building_size_range)) {
				$args['meta_query'][] = [
					'key'     => 'building_size',
					'value'   => array_values($building_size_range),
					'type'    => 'NUMERIC',
					'compare' => 'BETWEEN',
				];
			}
		}
		if (!empty($_POST['address'])) {
			$args['meta_query'][] = [
				'key'     => 'address',
				'value'   => $_POST['address'],
				'compare' => 'LIKE',
			];
		}

		if (count($args['meta_query']) > 1) {
			$args['meta_query']['relation'] = 'AND';
		}

		$query = new WP_Query($args);

		$response = [];

		if ($query->have_posts()) {
			while ($query->have_posts()) {
				$query->the_post();

				$property_type = fcre_get_option_label_from_meta(get_the_ID(), 'property_types', $this->FCRE->plugin_name . '-property-types');
				$transaction_type = fcre_get_option_label_from_meta(get_the_ID(), 'transaction_types', $this->FCRE->plugin_name . '-transaction-types');


				ob_start();
				fcre_get_template_part('fcre', 'property-loop-item');
				$property_html = ob_get_contents();
				ob_end_clean();
				$response[] = [
					'title' => get_the_title(),
					'link'  => get_permalink(),
					'property_type' => $property_type,
					'transaction_type' => $transaction_type,
					'property_html' => $property_html,
					'latitude' => get_post_meta(get_the_ID(), 'latitude', true),
					'longitude' => get_post_meta(get_the_ID(), 'longitude', true),
					'address' => get_post_meta(get_the_ID(), 'address', true),
					'building_size' => get_post_meta(get_the_ID(), 'building_size', true),
					'building_class' => get_post_meta(get_the_ID(), 'building_class', true),
					'lease_rate' => get_post_meta(get_the_ID(), 'lease_rate', true),
					'year_built' => get_post_meta(get_the_ID(), 'year_built', true),
					'property_status' => get_post_meta(get_the_ID(), 'status', true),
					'photo_gallery' => get_post_meta(get_the_ID(), 'photo_gallery', true)

				];
			}
		}

		wp_send_json_success($response);
	}

	public function submit_agreement()
	{
		$response = [];

		$property_id = $_POST['property_id'];
		$name = $_POST['name'];
		$phone = $_POST['phone'];
		$email = $_POST['email'];
		$date = $_POST['date'];
		$address = $_POST['address'];
		$city_state_zip = $_POST['city_state_zip'];

		$agent_emails = $_POST['agent_emails'];
		
		$property_title = get_the_title($property_id);
		$property_url = get_permalink($property_id);

		$cur_date = date('Y/m/d');

		try {
			$args = array(
				'post_type' => 'agreements',
				'posts_per_page' => -1,
			);
			$meta_query = array('relation' => 'AND');
			if (isset($email) && $email) {
				$meta_query[] = array(
					'key' => 'email',
					'value' => $email,
					'compare' => 'LIKE',
				);
			}

			if (isset($property_id) && $property_id) {
				$meta_query[] = array(
					'key' => 'property_id',
					'value' => $property_id,
					'type' => 'numeric',
					'compare' => '=',
				);
			}

			$args['meta_query'] = $meta_query;
			//global $wp_query;
			$query = new WP_Query($args);

			if ($query->have_posts()) {
				$cookie_name = "agreement_submit";
				$cookie_value = $email;
				setcookie($cookie_name, $cookie_value, time() + (86400 * 30 * 10), "/"); // 86400 = 1 day

				$response['message'] = 'Already exist';
			} else {
				$response['message'] = 'Record not found';

				$agreement = [
					'property_id' => $property_id,
					'agent_emails' => $agent_emails,
					'name' => $name,
					'phone' => $phone,
					'email' => $email,
					'date' => $date,
					'address' => $address,
					'city_state_zip' => $city_state_zip,
				];

				$agreement_id = wp_insert_post([
					'post_type' => $this->FCRE->agreements_custom_post_slug,
					'post_status' => 'publish',
					'post_title' => 'Agreement for Property ID: ' . $property_id,
				]);

				if ($agreement_id) {
					foreach ($agreement as $key => $value) {
						update_post_meta($agreement_id, $key, $value);
					}
				}

				$to = $email;
				$from =   get_bloginfo("admin_email");
				$subject = 'Confidentiality Agreement';
				$message = 'Thank you for your interest in our listing, your request to access the docs is pending, we will get back to you as soon as it is processed';

				$headers = "From: " . $from . "\r\n";
				$headers .= "MIME-Version: 1.0" . "\r\n";
				$headers .= "Content-Type: text/html; charset=UTF-8";

				$body = '';
				$body .= '<p> ' . $message . '</p>';
				$body .= '<p>Regards,<br> ' . get_bloginfo("name") . '</p>';

				if (wp_mail($to, $subject, $body, $headers)) {
					$response['email'] = 'Mail Send Successfully';
				} else {
					$response['email'] = 'Mail not send';
				}

				$admin_body = '';
				$admin_body .= 'Hi <br>';
				$admin_body .= '<p>New agreement signed, please find attachment</p>';
				$admin_body .= '<p>Property Title: ' . $property_title . '</p>';
				$admin_body .= '<p>Property URL: ' . $property_url . '</p>';
				$admin_body .= '<p>Name: ' . $name . '</p>';
				$admin_body .= '<p>Email: ' . $email . '</p>';
				$admin_body .= '<p>Phone: ' . $phone . '</p>';
				$admin_body .= '<p>Phone: ' . $date . '</p>';
				$admin_body .= '<p>Address: ' . $address . '</p>';
				$admin_body .= '<p>City, State Zip: ' . $city_state_zip . '</p>';


				$admin_email = $agent_emails;
				if (wp_mail($admin_email, $subject, $admin_body, $headers)) {
					$response['AgentEmail'] = 'Agent Mail Send Successfully';
				} else {
					$response['AgentEmail'] = 'Agent Mail not send';
				}

				$cookie_name = "agreement_submit";
				$cookie_value = $email;
				setcookie($cookie_name, $cookie_value, time() + (86400 * 30 * 10), "/"); // 86400 = 1 day
			}

		} catch (Exception $e) {
			$response['message'] = $e->getMessage();
		}

		wp_send_json_success($response);
	}

	public function check_signed_agreement()
    {
        $r = new stdClass();
        $email = $_POST['email'];
        $property_id = $_POST['property_id'];
        $args = array(
            'post_type' => $this->FCRE->agreements_custom_post_slug,
            'posts_per_page' => -1,
        );
        $meta_query = array('relation' => 'AND');
        if (isset($email) && $email) {
            $meta_query[] = array(
                'key' => 'email',
                'value' => $email,
                'compare' => '=',
            );
        }

        // if only max price is set
        if (isset($property_id) && $property_id) {
            $meta_query[] = array(
                'key' => 'property_id',
                'value' => $property_id,
                'type' => 'numeric',
                'compare' => '=',
            );
        }

        $args['meta_query'] = $meta_query;
        //global $wp_query;
        $query = new WP_Query($args);
        if ($query->have_posts()) {
            $r->message = 'Already exist';
            $r->status = true;
        	$cookie_name = "agreement_submit";
			$cookie_value = $email;
			setcookie($cookie_name, $cookie_value, time() + (86400 * 30 * 10), "/"); // 86400 = 1 day
        } else {
            $r->message = 'Record not found';
            $r->status = false;
        }
        wp_send_json($r);
    }

	public function submit_request_more_info()
	{
		$response = [];

		$property_id = $_POST['property_id'];
		$name = $_POST['name'];
		$email = $_POST['email'];
		$phone = $_POST['phone'];
		$message = $_POST['message'];
		$agent_emails = $_POST['agent_emails'];
		
		$property_title = get_the_title($property_id);
		$property_url = get_permalink($property_id);

		$cur_date = date('Y/m/d');

		try {
				$to = $email;
				$from = '<EMAIL>';
				$subject = 'Request More Info';
				$message = 'Thank you for your interest in our listing, your request to access the docs is pending, we will get back to you as soon as it is processed';

				$headers = "From: " . $from . "\r\n";
				$headers .= "MIME-Version: 1.0" . "\r\n";
				$headers .= "Content-Type: text/html; charset=UTF-8";

				$body = '';
				$body .= '<p> ' . $message . '</p>';
				$body .= '<p>Regards,<br> ' . get_bloginfo("name") . '</p>';

				if (wp_mail($to, $subject, $body, $headers)) {
					$response['email'] = 'Mail Send Successfully';
				} else {
					$response['email'] = 'Mail not send';
				}

				$admin_body = '';
				$admin_body .= 'Hi <br>';
				$admin_body .= '<p>New request for more info, please find attachment</p>';
				$admin_body .= '<p>Property Title: ' . $property_title . '</p>';
				$admin_body .= '<p>Property URL: ' . $property_url . '</p>';
				$admin_body .= '<p>Name: ' . $name . '</p>';
				$admin_body .= '<p>Email: ' . $email . '</p>';
				$admin_body .= '<p>Phone: ' . $phone . '</p>';
				$admin_body .= '<p>Message: ' . $message . '</p>';

				$admin_email = $agent_emails;
				if (wp_mail($admin_email, $subject, $admin_body, $headers)) {
					$response['admin_email'] = 'Admin Mail Send Successfully';
				} else {
					$response['admin_email'] = 'Admin Mail not send';
				}

		} catch (Exception $e) {
			$response['message'] = $e->getMessage();
		}

		wp_send_json_success($response);
	}
}
